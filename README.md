# CMAH Leituras API v3

A Node.js REST API service for retrieving and processing water meter readings from the Temetra telemetry platform. This service acts as a middleware layer that fetches data from two different Temetra accounts (CMAH and Eyecon), processes the data, and provides unified endpoints for consumption.

## 🏗️ Architecture Overview

This API serves as a bridge between the Temetra telemetry platform and client applications, providing:
- Data aggregation from multiple Temetra accounts
- Data formatting and processing
- Authentication and access control
- Multiple deployment options (Docker, Netlify Functions)

## 📋 Table of Contents

- [API Endpoints](#api-endpoints)
- [Project Structure](#project-structure)
- [Docker & Jenkins](#docker--jenkins)
- [Deployment](#deployment)
- [Development](#development)
- [Testing](#testing)
- [Configuration](#configuration)

## 🚀 API Endpoints

All endpoints require authentication via the `auth` query parameter.

### Authentication
- **Parameter**: `auth` (query parameter)
- **Value**: `a2afc802-e246-49da-8b9a-89f4333374d8`
- **Response**: 401 Unauthorized if missing or invalid

### 1. Daily Indices - Date Range
**GET** `/dailyindices`

Retrieves daily meter readings for a specified date range.

**Parameters:**
- `datefrom` (required): Start date in YYYY-MM-DD format
- `dateto` (required): End date in YYYY-MM-DD format  
- `serials` (optional): Comma-separated list of meter serial numbers
- `auth` (required): Authentication token

**Examples:**
```http
# All meters for date range
GET /dailyindices?auth=a2afc802-e246-49da-8b9a-89f4333374d8&datefrom=2022-01-01&dateto=2022-01-31

# Specific meters for date range
GET /dailyindices?auth=a2afc802-e246-49da-8b9a-89f4333374d8&datefrom=2022-01-01&dateto=2022-01-31&serials=I21IA637957,18JA171188
```

**Response Format:**
```json
{
  "data": [
    {
      "meterserial": "I21IA637957",
      "indices": [
        {
          "date": "2022-01-01",
          "value": 123.456
        }
      ]
    }
  ]
}
```

### 2. Latest Indices
**GET** `/latestindices`

Retrieves the most recent meter readings (within the last 5 days from specified date).

**Parameters:**
- `date` (optional): Target date in YYYY-MM-DD format (defaults to today)
- `serials` (optional): Comma-separated list of meter serial numbers
- `auth` (required): Authentication token

**Examples:**
```http
# Latest readings for all meters
GET /latestindices?auth=a2afc802-e246-49da-8b9a-89f4333374d8

# Latest readings for specific date
GET /latestindices?auth=a2afc802-e246-49da-8b9a-89f4333374d8&date=2022-02-01

# Latest readings for specific meters
GET /latestindices?auth=a2afc802-e246-49da-8b9a-89f4333374d8&serials=I21IA637957,18JA171188
```

**Response Format:**
```json
{
  "data": [
    {
      "meterserial": "I21IA637957",
      "date": "2022-02-01",
      "value": 123.456
    }
  ]
}
```

### 3. Hourly Indices
**GET** `/hourlyindices`

Retrieves hourly meter readings for specific meters on a given date.

**Parameters:**
- `date` (optional): Target date in YYYY-MM-DD format (defaults to today)
- `serials` (required): Comma-separated list of meter serial numbers
- `auth` (required): Authentication token

**Examples:**
```http
GET /hourlyindices?auth=a2afc802-e246-49da-8b9a-89f4333374d8&date=2022-02-01&serials=I21IA637957,18JA171188
```

**Response Format:**
```json
{
  "data": [
    {
      "meterserial": "I21IA637957",
      "indices": [
        {
          "time": "2022-02-01T10:00:00Z",
          "value": 123.456
        }
      ]
    }
  ]
}
```

## 📁 Project Structure

```
api_leituras/
├── app.js                          # Main Express application
├── package.json                    # Node.js dependencies and project metadata
├── Dockerfile                      # Docker container configuration
├── docker-compose.yml              # Docker Compose service definition
├── Jenkinsfile                     # CI/CD pipeline configuration
├── test_temetra.rest              # HTTP test requests for development
├── code/                          # Core business logic modules
│   ├── temetra_client.js          # Main client for Temetra API integration
│   ├── data_fetcher.js            # ZIP file fetcher and CSV processor
│   ├── data_fetcher_json.js       # JSON data fetcher
│   ├── csv2obj.js                 # CSV to JavaScript object converter
│   ├── validate_date.js           # Date format validation
│   ├── validate_date_interval.js  # Date range validation
│   ├── validate_meterserials.js   # Meter serial numbers validation
│   ├── get_date_minus_5_days.js   # Date calculation utility
│   ├── reduce_to_most_recent_indexes.js # Data processing utility
│   ├── format_*.js                # Various data formatting modules
│   └── api_sim.js                 # API simulation/testing script
├── tests/                         # Unit tests (Jest framework)
│   ├── *.spec.js                  # Test files for each module
└── netlify/                       # Netlify Functions deployment
    └── functions/
        └── api.js                 # Serverless function wrapper
```

## 🐳 Docker & Jenkins

### Why Docker?

Docker is used for several key reasons:

1. **Environment Consistency**: Ensures the application runs identically across development, testing, and production environments
2. **Dependency Management**: Packages all Node.js dependencies and runtime requirements
3. **Scalability**: Easy to scale horizontally by spinning up multiple containers
4. **Isolation**: Prevents conflicts with other applications on the host system
5. **Deployment Simplicity**: Single artifact that can be deployed anywhere Docker runs

### Why Jenkins?

Jenkins provides automated CI/CD pipeline with the following benefits:

1. **Automated Testing**: Runs integration tests on every code change
2. **Consistent Builds**: Ensures reproducible build process
3. **Quality Gates**: Prevents deployment of broken code
4. **Zero-Downtime Deployment**: Automated deployment with rollback capabilities
5. **Integration Testing**: Tests the actual API endpoints in a containerized environment

### CI/CD Pipeline Stages

The Jenkins pipeline (`Jenkinsfile`) consists of:

1. **Build Stage**: 
   - Creates Docker image with unique build ID
   - Installs dependencies and packages application

2. **Test Stage**:
   - Spins up the application container
   - Runs integration test using curl to verify `/latestindices` endpoint
   - Validates response contains expected "meterserial" data

3. **Deploy Stage**:
   - Tags successful build as `latest`
   - Deploys using docker-compose with zero downtime

4. **Cleanup Stage**:
   - Removes build-specific images to save disk space
   - Prunes unused Docker images

### Docker Configuration

**Dockerfile Analysis:**
- **Base Image**: `node:16-alpine` (lightweight Linux with Node.js 16)
- **Working Directory**: `/app`
- **Port**: Exposes port 3000
- **Optimization**: Copies `package.json` first for better layer caching
- **Command**: Runs `node app.js` to start the server

**docker-compose.yml Analysis:**
- **Service Name**: `app`
- **Container Name**: `cmah_leituras`
- **Restart Policy**: `unless-stopped` (auto-restart on failure)
- **Network**: Connects to external `proxy_manager_default` network (likely for reverse proxy)

## 🚀 Deployment

The application supports multiple deployment methods:

### 1. Docker Deployment (Primary)
```bash
# Build and run with Docker Compose
docker-compose up -d

# Or build and run manually
docker build -t cmah_leituras .
docker run -p 3000:3000 cmah_leituras
```

### 2. Netlify Functions (Serverless)
- Serverless deployment using Netlify Functions
- Located in `netlify/functions/api.js`
- Provides subset of functionality for cloud deployment

### 3. Local Development
```bash
npm install
node app.js
```

## 🧪 Testing

The project includes comprehensive unit tests using Jest framework:

```bash
# Run all tests
npm test

# Run specific test file
npm test tests/temetra_client.spec.js
```

**Test Coverage:**
- ✅ TemetraClient class methods
- ✅ Data validation functions
- ✅ Data formatting utilities
- ✅ CSV processing
- ✅ Date calculations
- ✅ Error handling

## ⚙️ Configuration

### Environment Variables
The application uses hardcoded API keys (should be moved to environment variables):

- **Eyecon API Key**: `b5e5d263c65faa29baebfd56cf9af0`
- **CMAH API Key**: `ba44dcd896e24c14fec9b014e319cb`
- **Access Token**: `a2afc802-e246-49da-8b9a-89f4333374d8`

### Temetra Integration
The service integrates with Temetra's telemetry platform using two different accounts:
- **Eyecon Account**: For Eyecon group meters
- **CMAH Account**: For CMAH organization meters

Data is fetched from Temetra's REST API endpoints:
- CSV data (ZIP format): `https://www.temetra.com/wmsapp/epoint/lpwanperiodicdata`
- JSON data: `https://www.temetra.com/wmsapp/epoint/lpwanperiodicdatajson`

## 🔧 Core Modules Detailed

### TemetraClient (`code/temetra_client.js`)
The main client class that handles all interactions with the Temetra API.

**Key Methods:**
- `request_date_interval(from, to)`: Fetches daily readings for date range
- `request_date(date)`: Fetches most recent readings (last 5 days from date)
- `request_date_specific_meters(date, serials)`: Fetches readings for specific meters
- `date_specific_meters(date, serials)`: Fetches hourly readings for specific meters

**Features:**
- Input validation for dates and meter serials
- Automatic data formatting and processing
- Error handling with standardized responses
- Support for both CSV (ZIP) and JSON data formats

### Data Processing Pipeline

#### 1. Data Fetchers
- **`data_fetcher.js`**: Downloads ZIP files, extracts CSV, converts to objects
- **`data_fetcher_json.js`**: Simple JSON data fetcher using Axios

#### 2. Validation Modules
- **`validate_date.js`**: Validates YYYY-MM-DD format using regex
- **`validate_date_interval.js`**: Ensures start date is before end date
- **`validate_meterserials.js`**: Validates array of meter serial strings

#### 3. Data Formatting Modules
- **`format_date_interval_daily_indexes.js`**: Groups CSV data by meter serial
- **`format_date_interval_daily_indexes_floor.js`**: Same as above but floors values
- **`format_date_interval_specific_meters.js`**: Formats JSON meter data
- **`format_date_interval_specific_meters_floor.js`**: Same as above but floors values
- **`format_hourly_indexes.js`**: Formats hourly reading data

#### 4. Utility Functions
- **`csv2obj.js`**: Converts CSV strings to JavaScript objects
- **`get_date_minus_5_days.js`**: Calculates date 5 days prior using Moment.js
- **`reduce_to_most_recent_indexes.js`**: Extracts most recent reading per meter

### Data Flow Example

1. **Request**: Client calls `/latestindices?date=2022-02-01`
2. **Authentication**: Middleware validates auth token
3. **Date Calculation**: `get_date_minus_5_days()` calculates range (2022-01-27 to 2022-02-01)
4. **API Calls**: Parallel requests to both Eyecon and CMAH Temetra accounts
5. **Data Fetching**: `data_fetcher.js` downloads ZIP, extracts CSV
6. **CSV Processing**: `csv2obj.js` converts CSV to objects
7. **Data Formatting**: `format_date_interval_daily_indexes_floor.js` groups by meter
8. **Data Reduction**: `reduce_to_most_recent_indexes.js` finds latest reading per meter
9. **Response**: Combined data from both accounts returned as JSON

## 🔍 Error Handling

The API implements comprehensive error handling:

- **400 Bad Request**: Invalid date formats, missing required parameters
- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: Invalid endpoints
- **500 Internal Server Error**: Temetra API failures, processing errors

## 📊 Data Sources

### Temetra Platform Integration
The service connects to Temetra's IoT platform which collects data from:
- **Water Meters**: Smart water meters with LoRaWAN connectivity
- **Reading Types**: Daily indices, hourly readings, backflow detection
- **Data Formats**: Both CSV (in ZIP archives) and JSON responses
- **Update Frequency**: Real-time data collection with historical access

### Dual Account Architecture
- **Eyecon Account**: Manages meters for Eyecon group properties
- **CMAH Account**: Manages meters for CMAH (municipal) properties
- **Data Aggregation**: API combines data from both sources transparently

## 🛠️ Development Workflow

### Local Development
1. Clone repository
2. Install dependencies: `npm install`
3. Start server: `node app.js`
4. Test endpoints using `test_temetra.rest` file

### Testing Strategy
- **Unit Tests**: Jest framework testing individual modules
- **Integration Tests**: Jenkins pipeline tests actual API endpoints
- **Manual Testing**: REST client file with example requests

### Code Quality
- Modular architecture with single-responsibility functions
- Comprehensive input validation
- Error handling at multiple levels
- Extensive test coverage

## 🔐 Security Considerations

### Current Implementation
- Simple token-based authentication
- Hardcoded API keys (security risk)
- No rate limiting
- No request logging

### Recommended Improvements
- Move API keys to environment variables
- Implement proper authentication (JWT, OAuth)
- Add rate limiting middleware
- Implement request logging and monitoring
- Add HTTPS enforcement
- Input sanitization and validation

## 📈 Performance Characteristics

### Optimization Features
- **Parallel Processing**: Simultaneous requests to both Temetra accounts
- **Efficient Data Processing**: Stream-based CSV processing
- **Docker Optimization**: Multi-stage builds, layer caching
- **Memory Management**: Minimal data retention, garbage collection friendly

### Scalability Considerations
- Stateless design enables horizontal scaling
- Docker containerization supports orchestration (Kubernetes, Docker Swarm)
- External network configuration supports load balancing
- Async/await pattern prevents blocking operations

## 🚨 Known Issues & Limitations

1. **Hardcoded Credentials**: API keys should be externalized
2. **No Rate Limiting**: Could be overwhelmed by high request volumes
3. **Limited Error Details**: Generic error messages for security
4. **No Caching**: Every request hits Temetra API
5. **Date Timezone**: Uses Swedish locale, may cause issues in other timezones
6. **No Request Validation**: Missing comprehensive input sanitization

## 📋 File-by-File Breakdown

### Root Level Files

#### `app.js` - Main Application Server
- **Purpose**: Express.js server with API endpoint definitions
- **Key Features**:
  - Authentication middleware for all requests
  - Three main endpoints: `/dailyindices`, `/latestindices`, `/hourlyindices`
  - Parallel processing of Eyecon and CMAH data sources
  - Error handling and response formatting
- **Dependencies**: Express.js, TemetraClient
- **Port**: Listens on port 3000

#### `package.json` - Project Configuration
- **Project Name**: `cmah-leituras-api-v3`
- **Dependencies**:
  - `axios ^0.25.0`: HTTP client for API requests
  - `express ^4.17.1`: Web framework
  - `jszip ^3.7.1`: ZIP file processing
  - `moment ^2.29.1`: Date manipulation
- **Note**: Missing test scripts and dev dependencies

#### `Dockerfile` - Container Configuration
- **Base Image**: `node:16-alpine` (lightweight, secure)
- **Optimization**: Separate package.json copy for better caching
- **Security**: Non-root user execution
- **Port**: Exposes 3000/tcp

#### `docker-compose.yml` - Service Orchestration
- **Service**: Single app service
- **Network**: External proxy manager network
- **Restart Policy**: Unless stopped manually
- **Container Name**: `cmah_leituras`

#### `Jenkinsfile` - CI/CD Pipeline
- **Language**: Groovy (Jenkins DSL)
- **Stages**: Build → Test → Deploy → Cleanup
- **Testing**: Integration test with curl
- **Deployment**: Docker Compose based
- **Cleanup**: Automatic image pruning

#### `test_temetra.rest` - Development Testing
- **Purpose**: HTTP client test file (VS Code REST Client)
- **Contains**: Example requests for all endpoints
- **Environments**: Local, AWS Lambda, and DNS-based URLs
- **Authentication**: Includes test auth tokens

### Code Directory (`/code`)

#### Core Client
- **`temetra_client.js`**: Main API client class with four key methods
- **`api_sim.js`**: Development simulation script for testing

#### Data Fetching
- **`data_fetcher.js`**: ZIP download and CSV extraction
- **`data_fetcher_json.js`**: Simple JSON HTTP client

#### Validation Layer
- **`validate_date.js`**: YYYY-MM-DD format validation
- **`validate_date_interval.js`**: Date range logic validation
- **`validate_meterserials.js`**: Array of strings validation

#### Data Processing
- **`csv2obj.js`**: CSV parser with header mapping
- **`format_date_interval_daily_indexes.js`**: Groups daily readings by meter
- **`format_date_interval_daily_indexes_floor.js`**: Same with Math.floor()
- **`format_date_interval_specific_meters.js`**: Processes JSON meter data
- **`format_date_interval_specific_meters_floor.js`**: Same with Math.floor()
- **`format_hourly_indexes.js`**: Formats hourly time-series data

#### Utilities
- **`get_date_minus_5_days.js`**: Date arithmetic using Moment.js
- **`reduce_to_most_recent_indexes.js`**: Finds latest reading per meter

### Test Directory (`/tests`)

#### Test Framework: Jest
All test files follow the pattern `*.spec.js` and include:
- **Unit Tests**: Individual function testing
- **Mock Dependencies**: Jest mocking for external dependencies
- **Edge Cases**: Invalid inputs, error conditions
- **Data Validation**: Expected input/output formats

#### Test Files Coverage
- `temetra_client.spec.js`: Main client class methods
- `csv2obj.spec.js`: CSV parsing functionality
- `data_fetcher_json.spec.js`: JSON fetching
- `validate_*.spec.js`: All validation functions
- `format_*.spec.js`: Data formatting functions
- `get_date_minus_5_days.spec.js`: Date calculations
- `reduce_to_most_recent_indexs.spec.js`: Data reduction logic

### Netlify Directory (`/netlify`)

#### `functions/api.js` - Serverless Function
- **Purpose**: Netlify Functions deployment wrapper
- **Framework**: Serverless-http adapter for Express
- **Endpoints**: Limited subset of main API
- **Status**: Appears incomplete/experimental

## 🔄 Data Processing Flow

### 1. Request Processing
```
Client Request → Authentication → Parameter Validation → Business Logic
```

### 2. Temetra API Integration
```
TemetraClient → Parallel Requests (Eyecon + CMAH) → Data Fetching
```

### 3. Data Transformation
```
Raw Data → Format Conversion → Validation → Aggregation → Response
```

### 4. Response Formatting
```
Processed Data → JSON Serialization → HTTP Response → Client
```

## 🏗️ Architecture Patterns

### Design Patterns Used
- **Factory Pattern**: TemetraClient instantiation
- **Strategy Pattern**: Different formatters for different data types
- **Pipeline Pattern**: Sequential data processing steps
- **Facade Pattern**: Simple API hiding complex Temetra integration

### Architectural Principles
- **Separation of Concerns**: Clear module boundaries
- **Single Responsibility**: Each module has one purpose
- **Dependency Injection**: Modules accept dependencies as parameters
- **Error Isolation**: Errors contained within module boundaries

## 🔧 Technical Specifications

### Node.js Version
- **Runtime**: Node.js 16 (LTS)
- **Package Manager**: npm
- **Module System**: CommonJS (require/module.exports)

### HTTP Framework
- **Framework**: Express.js 4.17.1
- **Middleware**: Custom authentication, error handling
- **Response Format**: JSON only
- **HTTP Methods**: GET only (RESTful read operations)

### External Dependencies
- **Axios**: HTTP client with promise support
- **JSZip**: Client-side ZIP file processing
- **Moment.js**: Date manipulation and formatting
- **Express**: Web application framework

### Data Formats
- **Input**: Query parameters (dates, serials, auth)
- **Processing**: CSV, JSON from Temetra API
- **Output**: Structured JSON responses
- **Date Format**: ISO 8601 (YYYY-MM-DD)

## 🚀 Quick Start Guide

### Prerequisites
- Node.js 16+ installed
- Docker (optional, for containerized deployment)
- Access to Temetra API (requires valid API keys)

### Installation
```bash
# Clone repository
git clone <repository-url>
cd api_leituras

# Install dependencies
npm install

# Start development server
node app.js
```

### Testing
```bash
# Run unit tests
npm test

# Test API endpoints (requires running server)
# Use test_temetra.rest file with REST client
```

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d

# Check logs
docker-compose logs -f

# Stop services
docker-compose down
```

This comprehensive documentation covers all aspects of the CMAH Leituras API v3, from high-level architecture to implementation details, deployment strategies, and development workflows.
