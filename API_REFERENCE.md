# CMAH Leituras API - Quick Reference

## Base URL
- **Local**: `http://localhost:3000`
- **Production**: `https://telemetria-cmah.eyecon-group.com`

## Authentication
All endpoints require the `auth` query parameter:
```
?auth=a2afc802-e246-49da-8b9a-89f4333374d8
```

## Endpoints

### 1. Daily Indices (Date Range)
```http
GET /dailyindices?datefrom=YYYY-MM-DD&dateto=YYYY-MM-DD&auth=TOKEN[&serials=SERIAL1,SERIAL2]
```

**Parameters:**
- `datefrom` (required): Start date
- `dateto` (required): End date  
- `serials` (optional): Comma-separated meter serials
- `auth` (required): Authentication token

**Response:**
```json
{
  "data": [
    {
      "meterserial": "I21IA637957",
      "indices": [
        {"date": "2022-01-01", "value": 123.456},
        {"date": "2022-01-02", "value": 124.789}
      ]
    }
  ]
}
```

### 2. Latest Indices
```http
GET /latestindices?auth=TOKEN[&date=YYYY-MM-DD][&serials=SERIAL1,SERIAL2]
```

**Parameters:**
- `date` (optional): Target date (defaults to today)
- `serials` (optional): Comma-separated meter serials
- `auth` (required): Authentication token

**Response:**
```json
{
  "data": [
    {
      "meterserial": "I21IA637957",
      "date": "2022-02-01",
      "value": 123.456
    }
  ]
}
```

### 3. Hourly Indices
```http
GET /hourlyindices?serials=SERIAL1,SERIAL2&auth=TOKEN[&date=YYYY-MM-DD]
```

**Parameters:**
- `serials` (required): Comma-separated meter serials
- `date` (optional): Target date (defaults to today)
- `auth` (required): Authentication token

**Response:**
```json
{
  "data": [
    {
      "meterserial": "I21IA637957",
      "indices": [
        {"time": "2022-02-01T10:00:00Z", "value": 123.456},
        {"time": "2022-02-01T11:00:00Z", "value": 124.789}
      ]
    }
  ]
}
```

## Error Responses

### 401 Unauthorized
```json
{"error": "Unauthorized"}
```

### 400 Bad Request
```json
{"error": "bad request"}
```

### 404 Not Found
```json
{"error": "Not Found"}
```

## Example Requests

### Get all meters for January 2022
```bash
curl "http://localhost:3000/dailyindices?datefrom=2022-01-01&dateto=2022-01-31&auth=a2afc802-e246-49da-8b9a-89f4333374d8"
```

### Get latest readings for specific meters
```bash
curl "http://localhost:3000/latestindices?serials=I21IA637957,18JA171188&auth=a2afc802-e246-49da-8b9a-89f4333374d8"
```

### Get hourly data for specific meters on specific date
```bash
curl "http://localhost:3000/hourlyindices?date=2022-02-01&serials=I21IA637957,18JA171188&auth=a2afc802-e246-49da-8b9a-89f4333374d8"
```

## Data Sources
- **Eyecon Account**: Eyecon group meters
- **CMAH Account**: Municipal meters
- **Combined**: API automatically aggregates data from both sources

## Notes
- All dates must be in YYYY-MM-DD format
- Latest indices returns readings from the last 5 days
- Hourly indices requires specific meter serials
- Values are automatically converted from raw units to standard units
- API handles both CSV (ZIP) and JSON data from Temetra platform
